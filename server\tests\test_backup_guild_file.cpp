// test_backup_guild_file.cpp - 测试BackupGuildFile功能
#include <gtest/gtest.h>
#include <memory>
#include <filesystem>
#include "../src/BaseObject/PlayObject.h"
#include "../src/GameEngine/GuildManager.h"
#include "../src/Common/Logger.h"

using namespace MirServer;

class BackupGuildFileTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志系统
        Logger::Initialize("test_backup_guild_file.log");
        
        // 初始化行会管理器
        GuildManager::GetInstance().Initialize();
        
        // 创建测试玩家
        player1 = std::make_shared<PlayObject>();
        player2 = std::make_shared<PlayObject>();
        player3 = std::make_shared<PlayObject>();
        
        // 设置玩家基本信息
        player1->SetCharName("GuildMaster");
        player1->SetLevel(50);
        
        player2->SetCharName("ViceLeader");
        player2->SetLevel(45);
        
        player3->SetCharName("Member1");
        player3->SetLevel(40);
        
        // 创建测试行会
        auto& guildManager = GuildManager::GetInstance();
        ASSERT_TRUE(guildManager.CreateGuild("TestGuildBackup", player1.get()));
        
        guild = guildManager.FindGuild("TestGuildBackup");
        ASSERT_NE(guild, nullptr);
        
        // 添加成员
        ASSERT_TRUE(guild->AddMember(player2.get(), GuildRank::VICE_CHIEF));
        ASSERT_TRUE(guild->AddMember(player3.get(), GuildRank::MEMBER));
        
        // 设置玩家行会信息
        player1->SetGuildInfo("TestGuildBackup", static_cast<BYTE>(GuildRank::CHIEF));
        player2->SetGuildInfo("TestGuildBackup", static_cast<BYTE>(GuildRank::VICE_CHIEF));
        player3->SetGuildInfo("TestGuildBackup", static_cast<BYTE>(GuildRank::MEMBER));
        
        // 添加一些测试数据
        guild->AddNotice("测试公告1");
        guild->AddNotice("测试公告2");
        guild->SetBuildPoint(1000);
        guild->SetAurae(500);
        guild->SetStability(800);
        guild->SetFlourishing(600);
        guild->SetGuildLevel(5);
        guild->SetGuildExp(25000);
        guild->SetGuildGold(100000);
        
        // 保存初始状态
        guild->SaveToFile();
    }
    
    void TearDown() override {
        // 清理测试文件
        std::filesystem::remove_all("GuildBase");
        
        // 清理
        GuildManager::GetInstance().Finalize();
        Logger::Finalize();
    }
    
    std::shared_ptr<PlayObject> player1;
    std::shared_ptr<PlayObject> player2;
    std::shared_ptr<PlayObject> player3;
    Guild* guild;
};

// 测试BackupGuildFile基本功能
TEST_F(BackupGuildFileTest, TestBasicBackupFunctionality) {
    // 验证初始状态
    EXPECT_EQ(guild->GetMemberCount(), 3);
    EXPECT_EQ(guild->GetNotices().size(), 2);
    EXPECT_EQ(guild->GetBuildPoint(), 1000);
    EXPECT_EQ(guild->GetGuildLevel(), 5);
    EXPECT_EQ(guild->GetGuildExp(), 25000);
    
    // 验证玩家行会信息
    EXPECT_EQ(player1->GetGuildName(), "TestGuildBackup");
    EXPECT_EQ(player2->GetGuildName(), "TestGuildBackup");
    EXPECT_EQ(player3->GetGuildName(), "TestGuildBackup");
    
    // 执行备份
    guild->BackupGuildFile();
    
    // 验证备份文件是否创建
    bool backupFileFound = false;
    bool configBackupFound = false;
    
    for (const auto& entry : std::filesystem::directory_iterator("GuildBase")) {
        std::string filename = entry.path().filename().string();
        if (filename.find("TestGuildBackup.") != std::string::npos && 
            filename.find(".bak") != std::string::npos) {
            if (filename.find(".ini.bak") != std::string::npos) {
                configBackupFound = true;
            } else {
                backupFileFound = true;
            }
        }
    }
    
    EXPECT_TRUE(backupFileFound) << "Backup file should be created";
    EXPECT_TRUE(configBackupFound) << "Config backup file should be created";
}

// 测试数据清理功能
TEST_F(BackupGuildFileTest, TestDataClearing) {
    // 执行备份
    guild->BackupGuildFile();
    
    // 验证行会数据被清理
    EXPECT_EQ(guild->GetMemberCount(), 0);
    EXPECT_EQ(guild->GetNotices().size(), 0);
    EXPECT_EQ(guild->GetBuildPoint(), 0);
    EXPECT_EQ(guild->GetAurae(), 0);
    EXPECT_EQ(guild->GetStability(), 0);
    EXPECT_EQ(guild->GetFlourishing(), 0);
    EXPECT_EQ(guild->GetGuildLevel(), 1);
    EXPECT_EQ(guild->GetGuildExp(), 0);
    EXPECT_EQ(guild->GetGuildGold(), 0);
    
    // 验证玩家行会信息被清理
    EXPECT_TRUE(player1->GetGuildName().empty());
    EXPECT_TRUE(player2->GetGuildName().empty());
    EXPECT_TRUE(player3->GetGuildName().empty());
    EXPECT_EQ(player1->GetGuildRank(), 0);
    EXPECT_EQ(player2->GetGuildRank(), 0);
    EXPECT_EQ(player3->GetGuildRank(), 0);
}

// 测试备份文件内容
TEST_F(BackupGuildFileTest, TestBackupFileContent) {
    // 执行备份
    guild->BackupGuildFile();
    
    // 查找备份文件
    std::string backupFile;
    for (const auto& entry : std::filesystem::directory_iterator("GuildBase")) {
        std::string filename = entry.path().filename().string();
        if (filename.find("TestGuildBackup.") != std::string::npos && 
            filename.find(".bak") != std::string::npos &&
            filename.find(".ini.bak") == std::string::npos) {
            backupFile = entry.path().string();
            break;
        }
    }
    
    ASSERT_FALSE(backupFile.empty()) << "Backup file should exist";
    
    // 读取备份文件内容
    std::ifstream file(backupFile);
    ASSERT_TRUE(file.is_open()) << "Should be able to open backup file";
    
    std::string content;
    std::string line;
    while (std::getline(file, line)) {
        content += line + "\n";
    }
    file.close();
    
    // 验证备份文件包含原始数据
    EXPECT_TRUE(content.find("测试公告1") != std::string::npos) << "Backup should contain notice 1";
    EXPECT_TRUE(content.find("测试公告2") != std::string::npos) << "Backup should contain notice 2";
    EXPECT_TRUE(content.find("GuildMaster") != std::string::npos) << "Backup should contain guild master";
    EXPECT_TRUE(content.find("ViceLeader") != std::string::npos) << "Backup should contain vice leader";
    EXPECT_TRUE(content.find("Member1") != std::string::npos) << "Backup should contain member";
}

// 测试配置文件备份
TEST_F(BackupGuildFileTest, TestConfigBackup) {
    // 设置一些配置
    guild->SetConfigBool("General.AutoSave", true);
    guild->SetConfigInt("General.MaxMembers", 150);
    guild->SetConfigString("General.Description", "测试行会");
    
    // 执行备份
    guild->BackupGuildFile();
    
    // 查找配置备份文件
    std::string configBackupFile;
    for (const auto& entry : std::filesystem::directory_iterator("GuildBase")) {
        std::string filename = entry.path().filename().string();
        if (filename.find("TestGuildBackup.") != std::string::npos && 
            filename.find(".ini.bak") != std::string::npos) {
            configBackupFile = entry.path().string();
            break;
        }
    }
    
    ASSERT_FALSE(configBackupFile.empty()) << "Config backup file should exist";
    
    // 读取配置备份文件
    std::ifstream file(configBackupFile);
    ASSERT_TRUE(file.is_open()) << "Should be able to open config backup file";
    
    std::string content;
    std::string line;
    while (std::getline(file, line)) {
        content += line + "\n";
    }
    file.close();
    
    // 验证配置备份包含设置的值
    EXPECT_TRUE(content.find("AutoSave=1") != std::string::npos) << "Config backup should contain AutoSave setting";
    EXPECT_TRUE(content.find("MaxMembers=150") != std::string::npos) << "Config backup should contain MaxMembers setting";
    EXPECT_TRUE(content.find("Description=测试行会") != std::string::npos) << "Config backup should contain Description setting";
}

// 测试重新加载功能
TEST_F(BackupGuildFileTest, TestReloadAfterBackup) {
    // 执行备份
    guild->BackupGuildFile();
    
    // 验证数据被清理
    EXPECT_EQ(guild->GetMemberCount(), 0);
    EXPECT_EQ(guild->GetNotices().size(), 0);
    
    // 重新加载（应该加载清理后的状态）
    EXPECT_TRUE(guild->LoadFromFile());
    
    // 验证加载的是清理后的状态
    EXPECT_EQ(guild->GetMemberCount(), 0);
    EXPECT_EQ(guild->GetNotices().size(), 0);
    EXPECT_EQ(guild->GetGuildLevel(), 1);
    EXPECT_EQ(guild->GetGuildExp(), 0);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
